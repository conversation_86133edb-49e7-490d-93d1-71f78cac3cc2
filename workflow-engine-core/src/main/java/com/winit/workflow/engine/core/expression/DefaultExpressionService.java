package com.winit.workflow.engine.core.expression;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;
import com.winit.workflow.engine.core.expression.function.FunctionRegistry;
import com.winit.workflow.engine.core.expression.provider.MvelExpressionEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 默认表达式服务实现
 */
public class DefaultExpressionService implements ExpressionService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultExpressionService.class);

    private final ExpressionEngine expressionEngine;

    public DefaultExpressionService(ExpressionEngine expressionEngine) {
        if (expressionEngine == null) {
            throw new IllegalArgumentException("ExpressionEngine cannot be null");
        }

        this.expressionEngine = expressionEngine;

        LOGGER.info("DefaultExpressionService initialized with engine: {}",
                expressionEngine.getClass().getName());
        LOGGER.info("Functions are now automatically registered via SPI mechanism");
    }

    @Override
    public Object parseExpression(String expression, Map<String, Object> context) {
        if (expression == null) {
            return null;
        }

        try {
            LOGGER.debug("Parsing expression: {}", expression);

            // 检查是否是表达式
            if (!expressionEngine.isExpression(expression)) {
                return expression;
            }

            // 解析表达式
            Object result = expressionEngine.evaluate(expression, context);
            LOGGER.debug("Expression result: {}", result);

            return result;
        } catch (Exception e) {
            LOGGER.error("Failed to parse expression: {}", expression, e);
            // 对于表达式解析失败，抛出异常而不是返回原始表达式
            // 这样可以更早地发现问题
            throw new RuntimeException("Expression parsing failed: " + expression, e);
        }
    }

    @Override
    public ExpressionEngine getExpressionEngine() {
        return expressionEngine;
    }

    @Override
    public void registerFunction(String name, Object function) {
        LOGGER.info("Registering function to service: {}", name);
        expressionEngine.registerFunction(name, function);
    }

    @Override
    public List<FunctionInfo> getAllFunctions() {
        LOGGER.debug("Getting all available functions");

        List<FunctionInfo> functionInfoList = new ArrayList<>();

        try {
            // 获取函数注册器
            FunctionRegistry functionRegistry = getFunctionRegistry();
            if (functionRegistry == null) {
                LOGGER.warn("Function registry is not available");
                return functionInfoList;
            }

            // 获取所有已注册的函数
            Map<String, ExpressionFunction> allFunctions = functionRegistry.getAllFunctions();

            // 转换为 FunctionInfo 对象
            for (Map.Entry<String, ExpressionFunction> entry : allFunctions.entrySet()) {
                ExpressionFunction function = entry.getValue();

                FunctionInfo functionInfo = new FunctionInfo(
                    function.getFunctionName(),
                    function.getDescription(),
                    function.getParameterCount(),
                    function.getPriority(),
                    function.getExample(),
                    FunctionExampleGenerator.getFunctionType(function),
                    function.getFunctionSignature()
                );

                functionInfoList.add(functionInfo);
            }

            LOGGER.info("Retrieved {} functions", functionInfoList.size());

        } catch (Exception e) {
            LOGGER.error("Error retrieving functions", e);
        }

        return functionInfoList;
    }

    /**
     * 获取函数注册器
     *
     * @return 函数注册器实例，如果不可用返回null
     */
    private FunctionRegistry getFunctionRegistry() {
        if (expressionEngine instanceof MvelExpressionEngine) {
            return ((MvelExpressionEngine) expressionEngine).getFunctionRegistry();
        }

        LOGGER.warn("Expression engine does not support function registry access: {}",
            expressionEngine.getClass().getName());
        return null;
    }

}