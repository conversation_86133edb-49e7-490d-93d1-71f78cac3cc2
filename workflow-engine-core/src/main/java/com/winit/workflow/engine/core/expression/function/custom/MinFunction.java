package com.winit.workflow.engine.core.expression.function.custom;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

/**
 * Minimum value function
 * Finds the minimum value from multiple arguments
 */
public class MinFunction implements ExpressionFunction {
    
    @Override
    public String getFunctionName() {
        return "min";
    }
    
    @Override
    public String getDescription() {
        return "Returns the minimum value from multiple arguments";
    }
    
    @Override
    public int getParameterCount() {
        return -1; // Variable parameters
    }
    
    @Override
    public int getPriority() {
        return 200; // Custom function priority
    }
    
    @Override
    public Object execute(Object... args) {
        if (args == null || args.length == 0) {
            throw new IllegalArgumentException("min function requires at least one argument");
        }
        
        Object minValue = null;
        double minNumeric = Double.POSITIVE_INFINITY;
        boolean hasNumeric = false;
        
        for (Object arg : args) {
            if (arg == null) {
                continue;
            }
            
            try {
                double numericValue = parseNumeric(arg);
                if (!hasNumeric || numericValue < minNumeric) {
                    minNumeric = numericValue;
                    minValue = arg;
                    hasNumeric = true;
                }
            } catch (NumberFormatException e) {
                // If we can't parse as number, compare as strings
                if (!hasNumeric) {
                    if (minValue == null || arg.toString().compareTo(minValue.toString()) < 0) {
                        minValue = arg;
                    }
                }
            }
        }
        
        return minValue;
    }
    
    private double parseNumeric(Object obj) throws NumberFormatException {
        if (obj instanceof Number) {
            return ((Number) obj).doubleValue();
        } else {
            return Double.parseDouble(obj.toString());
        }
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        return args != null && args.length > 0;
    }
    
    @Override
    public String getExample() {
        return "${min(1, 5, 3, 9, 2)}\n// Result: 1\n\n${min(3.14, 2.71, 1.41)}\n// Result: 1.41\n\n${min(value1, value2, value3)}\n// Returns minimum of variables";
    }
    
    @Override
    public String getFunctionSignature() {
        return "min(value1, value2, ...): returns the minimum value from arguments\nParameters: value1, value2, ... - values to compare (variable arguments)";
    }
}
