package com.winit.workflow.engine.core.expression.function.custom;

import com.winit.workflow.engine.core.expression.function.ExpressionFunction;

/**
 * Substring function
 * Extracts a substring from a string with specified start and optional end positions
 */
public class SubstringFunction implements ExpressionFunction {
    
    @Override
    public String getFunctionName() {
        return "substring";
    }
    
    @Override
    public String getDescription() {
        return "Extracts a substring from a string with specified start and optional end positions";
    }
    
    @Override
    public int getParameterCount() {
        return -1; // Variable parameters (2 or 3)
    }
    
    @Override
    public int getPriority() {
        return 200; // Custom function priority
    }
    
    @Override
    public Object execute(Object... args) {
        if (args == null || args.length < 2 || args.length > 3) {
            throw new IllegalArgumentException("substring function requires 2 or 3 arguments");
        }
        
        Object textArg = args[0];
        if (textArg == null) {
            return null;
        }
        
        String text = textArg.toString();
        int start = parseInteger(args[1]);
        Integer end = args.length > 2 ? parseInteger(args[2]) : null;
        
        int length = text.length();
        
        // Handle negative indices (count from end)
        if (start < 0) {
            start = Math.max(0, length + start);
        }
        
        if (end != null && end < 0) {
            end = Math.max(0, length + end);
        }
        
        // Validate bounds
        start = Math.max(0, Math.min(start, length));
        
        if (end == null) {
            end = length;
        } else {
            end = Math.max(start, Math.min(end, length));
        }
        
        try {
            return text.substring(start, end);
        } catch (StringIndexOutOfBoundsException e) {
            throw new IllegalArgumentException("Invalid substring indices: start=" + start + ", end=" + end + 
                ", text length=" + length);
        }
    }
    
    private int parseInteger(Object obj) {
        if (obj instanceof Number) {
            return ((Number) obj).intValue();
        } else {
            try {
                return Integer.parseInt(obj.toString());
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Cannot convert to integer: " + obj);
            }
        }
    }
    
    @Override
    public boolean validateParameters(Object... args) {
        return args != null && (args.length == 2 || args.length == 3);
    }
    
    @Override
    public String getExample() {
        return "${substring('Hello World', 0, 5)}\n// Result: \"Hello\"\n\n${substring('Hello World', 6)}\n// Result: \"World\" (from index 6 to end)\n\n${substring(text, -5)}\n// Result: last 5 characters";
    }
    
    @Override
    public String getFunctionSignature() {
        return "substring(text, start, end?): extracts substring from text\nParameters: text - source string, start - start index, end - end index (optional)";
    }
}
