package com.winit.workflow.engine.core.expression;

/**
 * 表达式函数信息类
 * 用于返回函数的详细信息，包括名称、描述、参数数量、优先级和使用示例
 * 
 * <AUTHOR>
 */
public class FunctionInfo {
    
    /**
     * 函数名称
     */
    private String functionName;
    
    /**
     * 函数描述
     */
    private String description;
    
    /**
     * 参数数量，-1表示可变参数
     */
    private int parameterCount;
    
    /**
     * 优先级，数值越小优先级越高
     */
    private int priority;
    
    /**
     * 使用示例
     */
    private String example;
    
    /**
     * 函数类型（内置函数或自定义函数）
     */
    private String type;

    /**
     * 函数签名
     */
    private String functionSignature;

    public FunctionInfo() {
    }

    public FunctionInfo(String functionName, String description, int parameterCount,
                       int priority, String example, String type, String functionSignature) {
        this.functionName = functionName;
        this.description = description;
        this.parameterCount = parameterCount;
        this.priority = priority;
        this.example = example;
        this.type = type;
        this.functionSignature = functionSignature;
    }
    
    public String getFunctionName() {
        return functionName;
    }
    
    public void setFunctionName(String functionName) {
        this.functionName = functionName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public int getParameterCount() {
        return parameterCount;
    }
    
    public void setParameterCount(int parameterCount) {
        this.parameterCount = parameterCount;
    }
    
    public int getPriority() {
        return priority;
    }
    
    public void setPriority(int priority) {
        this.priority = priority;
    }
    
    public String getExample() {
        return example;
    }
    
    public void setExample(String example) {
        this.example = example;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }

    public String getFunctionSignature() {
        return functionSignature;
    }

    public void setFunctionSignature(String functionSignature) {
        this.functionSignature = functionSignature;
    }

    @Override
    public String toString() {
        return "FunctionInfo{" +
                "functionName='" + functionName + '\'' +
                ", description='" + description + '\'' +
                ", parameterCount=" + parameterCount +
                ", priority=" + priority +
                ", example='" + example + '\'' +
                ", type='" + type + '\'' +
                ", functionSignature='" + functionSignature + '\'' +
                '}';
    }
}
