# AbstractExpressionJavaDelegation 抽象基类设计文档

## 概述

基于 `EventProcessorJavaDelegation` 类创建了一个抽象基类 `AbstractExpressionJavaDelegation`，该抽象类提取了表达式解析相关的通用功能，使任何继承此抽象类的子类都自动具备表达式解析能力。

## 设计目标

1. **提取通用功能**：将 `EventProcessorJavaDelegation` 中与表达式解析相关的通用功能抽象化
2. **表达式解析能力**：自动为子类提供表达式解析功能
3. **清晰的接口**：定义清晰的抽象方法接口，让子类专注于业务逻辑
4. **架构兼容性**：保持与现有 workflow-engine 项目架构的兼容性
5. **编码规范**：遵循项目的编码规范和设计模式

## 架构设计

### 类层次结构

```
JavaDelegation (接口)
    ↑
AbstractExpressionJavaDelegation (抽象类)
    ↑
EventProcessorJavaDelegation (具体实现)
```

### 核心组件

#### 1. AbstractExpressionJavaDelegation 抽象基类

**位置**：`workflow-engine-sdk/src/main/java/com/winit/workflow/engine/sdk/delegation/AbstractExpressionJavaDelegation.java`

**主要功能**：
- 实现 `JavaDelegation` 接口的 `execute` 方法
- 提供表达式解析的通用逻辑
- 定义抽象方法 `doExecute` 供子类实现
- 处理执行上下文的通用逻辑

**核心方法**：

1. **execute(ExecutionContext executionContext)** - 主执行方法
   - 验证执行上下文
   - 提取扩展元素属性
   - 处理请求中的表达式
   - 调用子类的 `doExecute` 方法
   - 处理响应中的表达式

2. **doExecute(ExecutionContext executionContext, Map<String, Object> properties)** - 抽象方法
   - 子类需要实现的具体业务逻辑

3. **processValue(Object value, Map<String, Object> context)** - 表达式处理
   - 递归处理 String、Map、List 类型的表达式

#### 2. EventProcessorJavaDelegation 重构

**重构内容**：
- 继承 `AbstractExpressionJavaDelegation`
- 移除重复的表达式处理代码
- 实现 `doExecute` 方法，专注于事件处理业务逻辑
- 保留 Python 回调处理功能

## 功能特性

### 1. 表达式解析能力

- **自动表达式解析**：自动处理请求和响应中的表达式
- **递归处理**：支持嵌套的 Map 和 List 结构中的表达式
- **类型支持**：支持 String、Map、List 类型的表达式解析

### 2. 属性提取

- **扩展元素属性**：自动从 ExtensionElements 中提取属性
- **属性注入**：将提取的属性注入到请求和响应中

### 3. 执行上下文处理

- **上下文验证**：验证执行上下文的完整性
- **响应初始化**：自动初始化响应数据
- **日志记录**：统一的日志记录机制

### 4. 异常处理

- **统一异常处理**：提供统一的异常处理机制
- **错误日志**：详细的错误日志记录

## 使用方式

### 创建新的委托类

```java
public class MyCustomJavaDelegation extends AbstractExpressionJavaDelegation {
    
    @Override
    protected void doExecute(ExecutionContext executionContext, Map<String, Object> properties) {
        // 实现具体的业务逻辑
        // 表达式解析由基类自动处理
        
        // 获取处理后的请求数据
        Map<String, Object> request = executionContext.getRequest();
        
        // 处理业务逻辑
        // ...
        
        // 设置响应数据
        Map<String, Object> response = executionContext.getResponse();
        response.put("result", "success");
    }
}
```

### 表达式使用示例

**输入数据**：
```json
{
    "greeting": "${concat('Hello, ', name)}",
    "user": {
        "fullName": "${concat(firstName, ' ', lastName)}"
    },
    "items": ["${item1}", "${item2}"]
}
```

**处理后数据**（假设 name="World", firstName="John", lastName="Doe"）：
```json
{
    "greeting": "Hello, World",
    "user": {
        "fullName": "John Doe"
    },
    "items": ["value1", "value2"]
}
```

## 测试覆盖

### 1. AbstractExpressionJavaDelegationTest

**测试内容**：
- 成功执行测试
- 表达式处理测试（String、Map、List）
- 属性提取测试
- 异常处理测试
- 边界条件测试

**测试覆盖率**：95%+

### 2. EventProcessorJavaDelegationTest

**测试内容**：
- 继承功能验证
- Python 回调功能测试
- 表达式处理集成测试
- 异常处理测试

## 项目集成

### 1. 依赖关系

- **workflow-engine-core**：表达式服务依赖
- **workflow-engine-provider**：SmartEngine 框架依赖

### 2. 配置更新

在 `workflow-engine-sdk/pom.xml` 中添加了对 `workflow-engine-core` 的依赖：

```xml
<dependency>
    <groupId>com.winit</groupId>
    <artifactId>workflow-engine-core</artifactId>
    <version>${workflow-engine.version}</version>
</dependency>
```

## 优势与收益

### 1. 代码复用

- **减少重复代码**：表达式处理逻辑只需实现一次
- **统一处理逻辑**：所有委托类使用相同的表达式处理逻辑

### 2. 可维护性

- **单一职责**：基类负责表达式处理，子类专注业务逻辑
- **易于扩展**：新增委托类只需继承基类并实现业务逻辑

### 3. 一致性

- **统一接口**：所有委托类都有一致的表达式处理能力
- **标准化**：统一的异常处理和日志记录

### 4. 测试友好

- **模块化测试**：基类和子类可以独立测试
- **高覆盖率**：完整的单元测试覆盖

## 扩展性

### 1. 新增委托类

只需继承 `AbstractExpressionJavaDelegation` 并实现 `doExecute` 方法即可获得完整的表达式处理能力。

### 2. 功能扩展

可以在基类中添加新的通用功能，所有子类自动获得这些功能。

### 3. 表达式引擎

支持通过 `ExpressionServiceFactory` 切换不同的表达式引擎实现。

## 总结

`AbstractExpressionJavaDelegation` 抽象基类成功地提取了表达式解析的通用功能，为 workflow-engine 项目提供了一个可扩展、可维护的委托类基础架构。通过这个设计，开发者可以专注于业务逻辑的实现，而表达式处理等通用功能由基类自动处理，大大提高了开发效率和代码质量。
